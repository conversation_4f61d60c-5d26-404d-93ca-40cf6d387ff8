package com.xtc.marketing.xtcmarketing.rpc.jingling.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 精灵分页响应DTO
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public abstract class PageJinglingResponse<T> extends BaseJinglingResponse {

    /**
     * 总记录数
     */
    @SerializedName("totalCount")
    private Integer totalCount;
    
    /**
     * 页面大小
     */
    @SerializedName("pageSize")
    private Integer pageSize;
    
    /**
     * 页面索引
     */
    @SerializedName("pageIndex")
    private Integer pageIndex;

    /**
     * 响应数据列表
     */
    @SerializedName("data")
    private List<T> data;

}
