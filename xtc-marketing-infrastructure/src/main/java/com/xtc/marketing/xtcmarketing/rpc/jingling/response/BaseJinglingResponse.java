package com.xtc.marketing.xtcmarketing.rpc.jingling.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 精灵基础响应DTO
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public abstract class BaseJinglingResponse {

    /**
     * 是否成功
     */
    @SerializedName("success")
    private Boolean success;

    /**
     * 错误代码
     */
    @SerializedName("errCode")
    private String errCode;

    /**
     * 错误信息
     */
    @SerializedName("errMessage")
    private String errMessage;

    /**
     * 判断请求失败
     *
     * @return 执行结果
     */
    public boolean failure() {
        return !success();
    }

    /**
     * 判断请求成功
     *
     * @return 执行结果
     */
    public boolean success() {
        return Boolean.TRUE.equals(success);
    }

}
