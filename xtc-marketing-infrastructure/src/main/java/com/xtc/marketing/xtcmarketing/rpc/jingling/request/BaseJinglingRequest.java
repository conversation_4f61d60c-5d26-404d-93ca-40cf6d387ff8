package com.xtc.marketing.xtcmarketing.rpc.jingling.request;

import com.google.gson.annotations.Expose;
import com.xtc.marketing.xtcmarketing.rpc.jingling.response.BaseJinglingResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.http.HttpMethod;

/**
 * 精灵基础请求类
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public abstract class BaseJinglingRequest<T extends BaseJinglingResponse> {

    /**
     * 授权令牌
     */
    @Expose
    private String authorization;
    /**
     * 应用ID
     */
    @Expose
    private String xtcAppId;

    /**
     * 获取 HTTP 请求方法
     *
     * @return HTTP 请求方法
     */
    public abstract HttpMethod getRequestMethod();

    /**
     * 获取接口路径
     *
     * @return 接口路径
     */
    public abstract String getRequestUrl();

    /**
     * 获取 HTTP 响应类型
     *
     * @return 响应类型
     */
    public abstract Class<T> getResponseClass();

}
