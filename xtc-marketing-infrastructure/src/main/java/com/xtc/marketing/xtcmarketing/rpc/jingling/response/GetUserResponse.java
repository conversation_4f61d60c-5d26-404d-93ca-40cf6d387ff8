package com.xtc.marketing.xtcmarketing.rpc.jingling.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * 获取用户信息响应DTO
 */
@Getter
@Setter
@ToString
public class GetUserResponse extends PageJinglingResponse<GetUserResponse.AgentInfo> {

    /**
     * 代理商信息
     */
    @Getter
    @Setter
    @ToString
    public static class AgentInfo {
        
        /**
         * 搜索值
         */
        @SerializedName("searchValue")
        private String searchValue;
        
        /**
         * 创建者
         */
        @SerializedName("createBy")
        private String createBy;
        
        /**
         * 创建时间
         */
        @SerializedName("createTime")
        private String createTime;
        
        /**
         * 更新者
         */
        @SerializedName("updateBy")
        private String updateBy;
        
        /**
         * 更新时间
         */
        @SerializedName("updateTime")
        private String updateTime;
        
        /**
         * 备注
         */
        @SerializedName("remark")
        private String remark;
        
        /**
         * 数据权限
         */
        @SerializedName("dataScope")
        private String dataScope;
        
        /**
         * 请求参数
         */
        @SerializedName("params")
        private Map<String, Object> params;
        
        /**
         * id
         */
        @SerializedName("id")
        private String id;
        
        /**
         * 客户id
         */
        @SerializedName("cusId")
        private Long cusId;
        
        /**
         * 客户名称
         */
        @SerializedName("cusName")
        private String cusName;
        
        /**
         * 客户代码
         */
        @SerializedName("cusCode")
        private String cusCode;
        
        /**
         * 国家
         */
        @SerializedName("country")
        private String country;
        
        /**
         * 地址
         */
        @SerializedName("address")
        private String address;
        
        /**
         * 销售区域 0:线下 1:电商
         */
        @SerializedName("saleArea")
        private String saleArea;
        
        /**
         * 机型
         */
        @SerializedName("catalog")
        private String catalog;
        
        /**
         * 状态 0启用 -1停止
         */
        @SerializedName("status")
        private Integer status;
        
        /**
         * 是否为旗舰店 0为否 1为是
         */
        @SerializedName("isFlagship")
        private Integer isFlagship;
        
        /**
         * 是否为海外 0为否 1为是
         */
        @SerializedName("isOverseas")
        private Integer isOverseas;
        
        /**
         * 联系人
         */
        @SerializedName("contact")
        private String contact;
        
        /**
         * 联系电话
         */
        @SerializedName("phone")
        private String phone;
        
        /**
         * 大客户名称
         */
        @SerializedName("bigCusName")
        private String bigCusName;
        
        /**
         * 大客户编码
         */
        @SerializedName("bigCusCode")
        private String bigCusCode;
        
        /**
         * 创建人
         */
        @SerializedName("createUser")
        private String createUser;
        
        /**
         * 创建时间
         */
        @SerializedName("createDate")
        private String createDate;
        
        /**
         * 同步时间
         */
        @SerializedName("syncDate")
        private String syncDate;
        
        /**
         * 代理id
         */
        @SerializedName("agentId")
        private String agentId;
        
        /**
         * 别名
         */
        @SerializedName("alias")
        private String alias;
        
        /**
         * 短信简称
         */
        @SerializedName("smsShortName")
        private String smsShortName;
        
        /**
         * 是否绩效考核 1是0否
         */
        @SerializedName("performanceFlag")
        private Integer performanceFlag;
        
        /**
         * 短信地区（可看其他省份）
         */
        @SerializedName("smsArea")
        private String smsArea;
        
        /**
         * 短信地区id
         */
        @SerializedName("smsAreaId")
        private String smsAreaId;
        
        /**
         * 发送实销数据的手机号码，只有一个
         */
        @SerializedName("saleDataPhone")
        private String saleDataPhone;
        
        /**
         * 发送实销数据的手机号码机主姓名
         */
        @SerializedName("saleDataPhonePeople")
        private String saleDataPhonePeople;
        
        /**
         * 发送实销数据的手机号码机主企业微信userid
         */
        @SerializedName("saleDataPhoneWechatUserid")
        private String saleDataPhoneWechatUserid;
        
        /**
         * 老板姓名
         */
        @SerializedName("director")
        private String director;
        
        /**
         * 老板手机号，多个逗号隔开
         */
        @SerializedName("directorPhone")
        private String directorPhone;
        
        /**
         * 接收业绩打分信息的主管姓名加手机号 如张三|13111111111,李四|13122222222
         */
        @SerializedName("recvPerfNamePhone")
        private String recvPerfNamePhone;
        
        /**
         * 接收业绩打分信息的主管的企业微信userid，多个逗号隔开
         */
        @SerializedName("recvPerfWechatUserid")
        private String recvPerfWechatUserid;
        
        /**
         * 发货邮件邮箱
         */
        @SerializedName("deliveryEmail")
        private String deliveryEmail;
        
        /**
         * 报表顺序
         */
        @SerializedName("reportOrder")
        private Long reportOrder;
        
        /**
         * 省份
         */
        @SerializedName("province")
        private String province;
        
        /**
         * 城市
         */
        @SerializedName("town")
        private String town;
        
        /**
         * 品牌 BBK XTC
         */
        @SerializedName("brand")
        private String brand;
        
        /**
         * 热力图权限微信ID
         */
        @SerializedName("hotMapWechatUserId")
        private String hotMapWechatUserId;
        
        /**
         * 热力图权限微信ID
         */
        @SerializedName("hotMapNamePhone")
        private String hotMapNamePhone;
        
        /**
         * 电商平台
         */
        @SerializedName("onlineType")
        private String onlineType;
        
        /**
         * 二级名称
         */
        @SerializedName("secondName")
        private String secondName;
        
        /**
         * 线上品牌
         */
        @SerializedName("onlineBrand")
        private String onlineBrand;
        
        /**
         * 是否下单 0：否 1：是
         */
        @SerializedName("isOrder")
        private Boolean isOrder;
    }

}
