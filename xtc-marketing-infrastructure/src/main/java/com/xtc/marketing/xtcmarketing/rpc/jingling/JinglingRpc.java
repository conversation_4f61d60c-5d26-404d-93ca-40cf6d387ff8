package com.xtc.marketing.xtcmarketing.rpc.jingling;

import com.xtc.marketing.xtcmarketing.exception.SysErrorCode;
import com.xtc.marketing.xtcmarketing.exception.SysException;
import com.xtc.marketing.xtcmarketing.rpc.jingling.request.BaseJinglingRequest;
import com.xtc.marketing.xtcmarketing.rpc.jingling.request.GetUserRequest;
import com.xtc.marketing.xtcmarketing.rpc.jingling.response.BaseJinglingResponse;
import com.xtc.marketing.xtcmarketing.rpc.jingling.response.GetUserResponse;
import com.xtc.marketing.xtcmarketing.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Optional;

/**
 * 精灵RPC
 */
@Slf4j
@Component
public class JinglingRpc {

    /**
     * 默认授权令牌
     */
    private static final String DEFAULT_AUTHORIZATION = "Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    /**
     * 默认应用ID
     */
    private static final String DEFAULT_XTC_APP_ID = "channel-replenish";
    /**
     * http 请求实例
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        requestFactory.setReadTimeout(5000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    /**
     * 根据微信用户ID获取代理商信息
     *
     * @param wechatUserId 微信用户ID
     * @return 代理商信息列表
     */
    public GetUserResponse.AgentInfo getAgentsByWechatId(String wechatUserId) {
        // 构建请求
        GetUserRequest request = new GetUserRequest();
        request.setPageSize(10000);
        request.setPageIndex(1);

        // 设置查询条件
        GetUserRequest.QueryCondition condition = new GetUserRequest.QueryCondition();
        condition.setField("recvPerfWechatUserid");
        condition.setCompareType("Like");
        condition.setValue(wechatUserId);
        request.setAndQuery(List.of(condition));

        // 调用接口
        try {
            GetUserResponse response = this.call(request);
            if (CollectionUtils.isNotEmpty(response.getData())) {
                return response.getData().getFirst();
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("获取精灵代理商信息失败", e);
            return Optional.empty();
        }
    }

    /**
     * 调用接口
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 响应
     */
    private <T extends BaseJinglingResponse> T call(BaseJinglingRequest<T> request) {
        // 初始化
        RequestEntity<?> requestEntity = this.createRequestEntity(request);
        String responseStr = "";
        String requestInfoLog = "精灵RPC %s url: %s, header: %s, body: %s"
                .formatted(requestEntity.getMethod(), requestEntity.getUrl(), requestEntity.getHeaders(), requestEntity.getBody());
        try {
            // 发起请求
            log.info(requestInfoLog);
            ResponseEntity<String> responseEntity = REST_TEMPLATE.exchange(requestEntity, String.class);
            responseStr = responseEntity.getBody();
            log.info("{}, response: {}", requestInfoLog, responseStr);
            // 解析响应
            T response = GsonUtil.jsonToBean(responseStr, request.getResponseClass());
            if (response.failure()) {
                throw this.remoteException();
            }
            return response;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, requestEntity, e);
        }
    }

    /**
     * 生成请求
     *
     * @param request 请求
     * @param <T>     响应类型
     * @return 请求
     */
    private <T extends BaseJinglingResponse> RequestEntity<?> createRequestEntity(BaseJinglingRequest<T> request) {
        // 设置默认值
        if (StringUtils.isBlank(request.getAuthorization())) {
            request.setAuthorization(DEFAULT_AUTHORIZATION);
        }
        if (StringUtils.isBlank(request.getXtcAppId())) {
            request.setXtcAppId(DEFAULT_XTC_APP_ID);
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("authorization", request.getAuthorization());
        headers.set("xtc-app-id", request.getXtcAppId());

        // 构建请求体
        String requestBody = null;
        if (request.getRequestMethod() == HttpMethod.POST) {
            requestBody = GsonUtil.objectToJson(request);
        }

        // 构建请求
        return new RequestEntity<>(requestBody, headers, request.getRequestMethod(), URI.create(request.getRequestUrl()));
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @return 异常
     */
    private SysException rpcSysException(String message) {
        String msg = "精灵RPC异常 message: %s".formatted(message);
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr   响应结果
     * @param requestEntity 请求实体
     * @param e             异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, RequestEntity<?> requestEntity, Exception e) {
        String msg = "精灵RPC异常 response: %s, %s url: %s".formatted(responseStr, requestEntity.getMethod(), requestEntity.getUrl());
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg, e);
    }

    public static void main(String[] args) {
        JinglingRpc rpc = new JinglingRpc();
        try {
            Optional<List<GetUserResponse.AgentInfo>> result = rpc.getAgentsByWechatId("865adf33751f400082b56f021d97578f");
            if (result.isPresent()) {
                System.out.println("获取到的代理商列表: " + result.get());
            } else {
                System.out.println("未获取到代理商信息");
            }
        } catch (Exception e) {
            System.err.println("调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

}
